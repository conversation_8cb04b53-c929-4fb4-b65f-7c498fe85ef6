{"name": "games_club", "version": "0.0.1", "description": "Games club Web SPA", "productName": "games_club", "author": "Mustafa Online <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.vue ./", "test": "echo \"No test specified\" && exit 0"}, "dependencies": {"@quasar/extras": "^1.10.4", "axios": "^0.18.1", "axios-extensions": "^2.0.3", "core-js": "^3.11.1", "pdfvuer": "^1.7.5", "quasar": "^1.15.12", "vue-glide-js": "^1.3.14", "vue-pdf-app": "^2.0.0", "vue-plyr": "^7.0.0", "vuejs-loadmore": "^1.0.7"}, "devDependencies": {"@quasar/app": "^2.2.5", "babel-eslint": "^10.0.1", "eslint": "^6.8.0", "eslint-config-standard": "^14.1.0", "eslint-loader": "^3.0.3", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.1.2"}, "browserslist": ["last 10 Chrome versions", "last 10 Firefox versions", "last 4 Edge versions", "last 7 Safari versions", "last 8 Android versions", "last 8 ChromeAndroid versions", "last 8 FirefoxAndroid versions", "last 10 iOS versions", "last 5 Opera versions"], "engines": {"node": ">= 10.18.1", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}