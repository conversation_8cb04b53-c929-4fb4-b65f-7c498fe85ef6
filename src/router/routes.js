const routes = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/Index.vue'), name: 'home-page' },
      { path: 'login', component: () => import('pages/AuthLogin.vue') },
      { path: 'about', component: () => import('pages/About.vue') },
      { path: 'terms', component: () => import('pages/Terms.vue') },
      { path: 'library', component: () => import('pages/Library.vue') },
      { path: 'search', component: () => import('pages/Search.vue') },
      { path: 'subscribe', component: () => import('pages/Subscribe.vue') },
      { path: 'thank-you', component: () => import('pages/ThankYou.vue'), name: 'thank-you' },
      { path: 'game-intro', component: () => import('pages/GameIntro.vue') },
      {
        path: 'game-list',
        component: () => import('pages/Games.vue'),
        name: 'game_list',
        children: [
          { path: ':id', component: () => import('pages/Game.vue'), props: true, name: 'game_single' }
        ]
      },
      {
        path: 'categories',
        component: () => import('pages/Categories.vue'),
        name: 'category_list',
        children: [
          { path: ':id', component: () => import('pages/Category.vue'), props: true, name: 'category_single' }
        ]
      }
    ]
  },

  {
    path: '/auth',
    component: () => import('layouts/LayoutAuth.vue'),
    // beforeEnter: (to, from, next) => {
    //   // eslint-disable-next-line no-constant-condition
    //   if (localStorage.getItem('gamek_the_access_token') != null && localStorage.getItem('gamek_the_access_token').length > 100) {
    //     next({ path: '/' })
    //   } else {
    //     next()
    //   }
    // },
    children: [
      { path: 'otp', component: () => import('pages/AuthOTP.vue') }
    ]
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '*',
    component: () => import('pages/Error404.vue')
  }
]

export default routes
