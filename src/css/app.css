/* app global css */
body {
  background-color: #FFF;
  color: #4A4848;
}

body .q-drawer {
  background: url(../../public/games-bg.jpg) top !important;
}
body .q-drawer .q-drawer__content {
    background: rgba(255, 255, 255, 0.97) !important;
    padding-top: 1rem;
}
.nav-logo  {
  color: #FFF;
  font-family: 'BungeeInline-Regular' !important;
}
.drawer-logo  {
  color: #bacc07 !important;
  font-family: 'BungeeInline-Regular' !important;
  font-size: 1.4rem;
  margin-top: 2rem !important;
}
.games_categories .games_category:last-child {
    display: none
}
.bg-primary {
    background: #bacc07 !important;
}
.q-drawer .q-item {
  font-size: 1.2rem !important;
}
.q-item.q-router-link--active, .q-item--active {
  color: #222 !important;
}
.max {
max-width: 100%;
}
.bg-theme-grad {
    background: linear-gradient(45deg, #974091, #b1216e);
}
.game-box img {
    border-radius: 6px;
}
.the-box {
    max-width: 90%;
    margin-right: auto;
    margin-left: auto;
}
.rad-10 {
    border-radius: 10px;
}
.cat_title {
    border-bottom: 3px solid #bd3a7c;
    display: table;
    margin: 0px auto 12px auto;
    text-align: center;
}
.centro {
    vertical-align: middle;
    display: flex;
    justify-content: center;
    align-items: center;
}
.title-theme { color: #0b60ae }
.subtitle-theme { color: #7c7c7c }
* {letter-spacing: 0 !important}
a {text-decoration: none}
.game_single .q-page-container,
.game_single .q-page {
    padding: 0 !important;
}
.home-page {
    background: #FFF;
}
.lister {
    background: #FFF;
}
.pager {
background: #cecece52;
outline: 0;
border: 0;
font-size: 17px;
cursor: pointer;
width: 30px;
height: 30px;
line-height: 30px;
padding: 0;
box-shadow: 0 4px 5px #ccc;
}

.real_hero {
    background-color: #bacc07 !important;
}
.book_view .the_footer,
.book_single .the_footer,
.game_single .the_footer
{
    display: none !important
}
.plain { unicode-bidi: plaintext !important }

.text-gold {
    color: #d0af47;
}

.library_pg .q-avatar {
    font-size: 80px !important;
    border-radius: 0 !important;
    height: auto;
 }
.library_pg .q-item {
    padding: 8px 12px;
}
.hero img {
    max-width: 100%;
    margin: 0 auto;
    border-radius: 10px;
}
.home-slider h6 {
    font-size: 1rem;
    font-weight: 800;
}
.bg-darkx {
    background: #bacc07;
}
.max-w-100 {
    max-width: 100%;
}

.home-slider {
    margin: 0 auto;
    border-radius: .5rem;
}

.home-slider a{
    display: block !important;
    width: 100% !important;
}

.the-title {
display: inline-block;
position: relative;
}

.the-title:after {
    content: "";
    width: 100%;
    height: 3px;
    background: #333;
    display: block;
    position: absolute;
    bottom: -2px;
}

body .glide--rtl {
direction: ltr;
}

.gld-categ .q-btn__wrapper {
    padding: 4px 6px;
}

ul.glide__slides {
    margin: 0 auto;
}

aside.q-drawer {
    max-width: 300px;
}

.book_holder {
margin-bottom: 110px;
}
.book_hero_s {
width: 90%;
right: 0;
left: 0;
top: 0;
margin: 0 auto;
border-radius: .5rem;
background: #fff;
}

.mx-1 {
    margin-right: 5px;
    margin-left: 5px;
}

/* Player */
.the_player_sec {
    direction: ltr !important /* rtl:ignore */;
}

.q-page {
    min-height: 85vh !important;
    /* padding: 0 !important; */
}

.about_pg + .the_footer {
    position: fixed;
    width: 100%;
    bottom: 0;
}

.game-name {
  background-color: #0063bf !important;
  color: #FFF;
  border-radius: 5px;
  padding: 5px 10px !important;
  font-weight: bold;
}
