import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default function (/* { ssrContext } */) {
  const Store = new Vuex.Store({
    state: {
      user: null
    },
    getters: {
      user: (state) => {
        return state.user
      }
    },
    actions: {
      user (context, user) {
        context.commit('user', user)
      }
    },
    mutations: {
      user (state, user) {
        state.user = user
      }
    },

    // enable strict mode (adds overhead!)
    // for dev mode only
    strict: process.env.DEBUGGING
  })

  return Store
}
