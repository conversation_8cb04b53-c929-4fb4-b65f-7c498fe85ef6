<template>
  <q-layout view="lHh Lpr lFf" :class="$router.currentRoute.name">
    <q-header class="bg-darkx q-py-none">
      <q-toolbar>
        <q-btn flat dense round icon="menu" aria-label="Menu" @click="leftDrawerOpen =!leftDrawerOpen" />
        <div class="col">
          <router-link to="/" class="text-white nav-logo" style="font-size: 1.7rem">GAMEK</router-link>
        </div>
        <!-- <q-toolbar-title class="q-pr-none  q-pl-xs">
        </q-toolbar-title> -->
        <q-btn v-if="this.$store.getters.user == null" to="/login" flat color="white" class="q-my-md" style="color: #333 !important; font-size: 14px" label="الدخول" />
        <q-btn v-if="this.$store.getters.user == null" @click="toLP" color="purple" class="q-my-md q-ml-none q-px-none" style="font-size: 14px" label="الإشتراك" />
        <q-btn v-if="this.$store.getters.user != null" @click="unsubscribeConfirm=true" color="red" class="q-mx-md q-my-md" style="font-size: 14px" label="إلغاء اﻹشتراك" />
        <q-btn v-if="this.$store.getters.user != null" @click="logout" color="purple" class="q-my-md" style="font-size: 14px" label="خروج" />
        <!-- <img src="Sudani-Logo.png" style="max-height: 60px" alt="" class="q-ml-lg"> -->
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      content-class="bg-grey-1"
      class="deep-purple-2"
    >
      <q-list>
        <q-item-label
          header
          class="text-grey-8"
        >
        <router-link to="/" class="text-dark drawer-logo">GAMEK</router-link>
        </q-item-label>
        <EssentialLink
          v-for="link in essentialLinks"
          :key="link.title"
          v-bind="link"
          class="text-grey-7"
        />
      </q-list>
      <q-item clickable @click="toLP" exact v-if="this.$store.getters.user == null">
        <q-item-section avatar>
          <q-icon name="login" />
        </q-item-section>
        <q-item-section>
          <q-item-label style="color:green">اﻹشتراك في المنصة</q-item-label>
        </q-item-section>
      </q-item>
      <q-item clickable @click="unsubscribeConfirm=true" exact v-if="this.$store.getters.user != null && this.$store.getters.user.is_subscribed == true">
        <q-item-section avatar>
          <q-icon name="person_remove" />
        </q-item-section>
        <q-item-section>
          <q-item-label>إلغاء الإشتراك</q-item-label>
        </q-item-section>
      </q-item>
  <q-item clickable @click="logout" exact v-if="this.$store.getters.user != null">
    <q-item-section avatar>
      <q-icon name="logout" />
    </q-item-section>
    <q-item-section>
      <q-item-label>خروج</q-item-label>
    </q-item-section>
  </q-item>
    </q-drawer>
    <q-dialog v-model="unsubscribeConfirm" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar size="md" icon="person_remove" color="orange" text-color="white" />
          <span class="q-ml-sm">هل أنت متأكد من إلغاء الإشتراك؟</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="تراجع" color="primary" v-close-popup />
          <q-btn label="إلغاء الإشتراك" color="negative" v-close-popup @click="unsubscribe" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-page-container>
      <router-view />
      <theFooter />
    </q-page-container>
  </q-layout>
</template>

<script>
import EssentialLink from 'components/EssentialLink.vue'
import theFooter from 'components/Footer.vue'
var baseUrl = 'https://gamezone.digitalservices.website/api/v1.0/spay/'

const linksData = [
  {
    title: 'الرئيسية',
    icon: 'home',
    link: '/'
  },
  // {
  //   title: 'عن المنصة',
  //   icon: 'info',
  //   link: '/about'
  // },
  {
    title: 'التصنيفات',
    icon: 'list',
    link: '/categories'
  }
]

export default {
  name: 'MainLayout',
  components: { EssentialLink, theFooter },
  data () {
    return {
      leftDrawerOpen: false,
      essentialLinks: linksData,
      playerShow: false,
      unsubscribeConfirm: false
    }
  },
  created () {
    this.checkToken()
    this.me()
  },
  methods: {
    toLP () {
      window.location.assign('http://sudanipay.com/subscription/gaamek/gaamek/')
    },
    me () {
      this.$axios({
        method: 'post',
        url: 'spay/check-subscription',
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        }
      }).then(res => {
        this.$store.dispatch('user', res.data)
        console.log(this.$store.getters.user)
      })
    },
    checkToken () {
      if (this.$route.query.t != null && this.$route.query.t.length > 150) {
        localStorage.setItem('gamek_the_access_token', this.$route.query.t)
        localStorage.setItem('gamek_the_phone', this.$route.query.p)
        if (!localStorage.getItem('after_redirect')) {
          localStorage.after_redirect = true
          window.location.assign('/')
        } else {
          localStorage.removeItem('after_redirect')
        }
      }
    },
    logout () {
      this.$axios({
        method: 'post',
        url: 'auth/logout',
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        }
      }).then(res => {
        localStorage.removeItem('gamek_the_access_token')
        this.$store.dispatch('user', null)
        this.$router.push('/')
      })
    },
    unsubscribe () {
      this.$axios({
        method: 'post',
        url: baseUrl + 'unsubscribe',
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        },
        data: {
          msisdn: localStorage.getItem('gamek_the_phone')
        }
      }).then(res => {
        localStorage.removeItem('gamek_the_access_token')
        this.$router.push('/')
        window.location.reload(true)
      })
    }
  },
  computed: {
    currentRouteName () {
      return this.$route.name
    }
  }
}
</script>
