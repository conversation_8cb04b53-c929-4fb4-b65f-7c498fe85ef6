<template>
  <div class="the_footer bg-darkx q-py-sm q-px-lg">
    <div class="container text-center">
      <!-- <div class="social q-my-sm">
        <a href="https://facebook.com/ZainSudan" target="_blank" class="q-mx-xs q-pt-sm"><img src="https://www.freepnglogos.com/uploads/facebook-icons/facebook-icon-simple-iconset-0.png" width="26px"  alt=""></a>
        <a href="https://twitter.com/ZainSudan" target="_blank" class="q-mx-xs q-pt-sm"><img src="https://www.freepnglogos.com/uploads/twitter-logo-png/twitter-bird-twitter-button-bird-png-logo-22.png" width="26px"  alt=""></a>
        <a href="https://www.instagram.com/zainsudan/" target="_blank" class="q-mx-xs q-pt-sm"><img src="https://www.freepnglogos.com/uploads/instagram-logos-png-images-free-download-5.png" width="26px"  alt=""></a>
      </div> -->
      <!-- <div>
        Developed in Khartoum Sudan by Digitalteq Plus
      </div> -->
      <div class="flex justify-between">
        <div></div>
        <div></div>
        <div class="copyright q-pt-sm text-white">
          جميع الحقوق محفوظة &copy; {{ new Date().getFullYear() }}
        </div>

        <div>
          <img src="sudani-new.png" style="max-height: 45px; filter: brightness(0) invert(1)" alt="" class="q-ml-lg">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'theFooter',
  data () {
    return {}
  }
}
</script>

<style scoped>
.the_footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
}
</style>
