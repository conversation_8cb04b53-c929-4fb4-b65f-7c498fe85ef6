<template>
    <div class="top-search">
      <form @submit.prevent="submit">
        <q-input dark dense standout v-model="query" input-class="query-right" class="" placeholder="بحث" required>
          <template v-slot:append>
            <q-icon v-if="query === ''" name="search" color="text-success" />
            <q-icon v-else name="clear" class="cursor-pointer" @click="query = ''" />
          </template>
        </q-input>
      </form>
    </div>
</template>

<script>
export default {
  data () {
    return {
      query: this.$route.query.q || ''
    }
  },
  methods: {
    submit () {
      // this.$router.push('/search?q=' + this.query)
      this.$emit('submit', this.query)
    }
  }
}
</script>

<style lang="scss" scoped>
  .top-search {
    background-color: #b4c607;
    padding: 1rem 1rem;
  }

  .top-search .q-field  {

  }
</style>
