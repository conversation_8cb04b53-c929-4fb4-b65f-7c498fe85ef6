<template>
  <div dir="ltr" style="direction: ltr" class="the_player_sec q-pa-sm q-px-md">
        <vue-plyr :options="plyrOptions">
          <audio controls playsinline autoplay>
            <source src="" />
          </audio>
    </vue-plyr>
      </div>
</template>

<script>
import VuePlyr from 'vue-plyr'
/* rtl:ignore */
import 'vue-plyr/dist/vue-plyr.css'
export default {
  name: 'bookPlayer',
  data () {
    return {
      plyrOptions: {
        settings: [],
        controls: ['play', 'progress', 'duration', 'current-time']
      }
    }
  },
  components: { VuePlyr }
}
</script>
