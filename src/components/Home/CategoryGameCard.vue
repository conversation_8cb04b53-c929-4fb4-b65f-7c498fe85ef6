<template>
  <div class="slider-holderx hero" v-show="skeleton || games.length" >
    <div class="row centro q-px-md">
      <div class="col text-h6 title-theme text-weight-bold text-dark q-px-lg">{{ category.name_ar }}</div>
      <div class="col subtitle-theme text-right">
        <router-link :to="'/categories/' + category.id" class="text-">
        المزيد
        </router-link>
      </div>
    </div>
    <div class="holder relative q-py-sm lister q-px-md">
      <div class="home-slider g-list game-box shadow q-pa-sm q-py-sm">
        <vue-glide v-if="games && games.length" v-bind="sliderConfig">
          <vue-glide-slide v-for="game in games" :key="game.id">
            <div class="rowd text-center">
              <router-link :to="'/game-list/' + game.id" class="text-white">
              <img class="shadow-2" width="100%" :src="'https://gamezone.digitalservices.website' + game.thumbnail" :alt="game.name_ar">
              <p class="q-mb-none q-mt-smX game-name">{{ game.name_ar }}</p>
              </router-link>
            </div>
          </vue-glide-slide>
        </vue-glide>
        <div class="row" v-if="skeleton">
          <div class="col-6 col-md-3">
            <div>
              <div>
                <q-skeleton type="QSlider" height="150px" />
              </div>

              <div>
                  <q-skeleton type="text" height="50px" />
              </div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div>
              <div>
                <q-skeleton type="QSlider" height="150px" />
              </div>

              <div>
                <q-skeleton type="text" height="50px" />
              </div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div>
              <div>
                <q-skeleton type="QSlider" height="150px" />
              </div>

              <div>
                <q-skeleton type="text" height="50px" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { http } from 'src/boot/axios'
var categoriesUrl = 'https://gamezone.digitalservices.website/api/v1.0/categories'
import { Glide, GlideSlide } from 'vue-glide-js'
import 'vue-glide-js/dist/vue-glide.css'
export default {
  props: ['category'],
  data: () => {
    return {
      skeleton: true,
      games: [],
      sliderConfig: {
        perView: 4,
        direction: 'rtl',
        autoplay: 2500,
        dragThreshold: 80,
        swipeThreshold: 120,
        rewind: true,
        bound: true,
        animationDuration: 900,
        breakpoints: {
          800: {
            perView: 2
          }
        }
      }
    }
  },
  mounted () {
    this.getCategoryGames()
  },
  methods: {
    getCategoryGames () {
      http.get(categoriesUrl + '/' + this.category.id + '/games', {
        headers: {
          'Content-Type': 'application/json'
        //   // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        },
        cache: true
      }).then(res => {
        this.games = res.data.data
        this.skeleton = false
      })
    }
  },
  components: {
    [Glide.name]: Glide,
    [GlideSlide.name]: GlideSlide
  }
}
</script>
