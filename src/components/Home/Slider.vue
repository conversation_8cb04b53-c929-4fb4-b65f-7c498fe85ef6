<template>
  <div class="slider-holder real_hero hero q-mb-lg" v-if="slideList.length">
    <div class="holder relative q-py-sm q-px-md">
      <div class="home-slider bg-purple-1 q-pa-sm q-py-sm">
        <vue-glide v-if="slideList && slideList.length" v-bind="sliderConfig">
        <vue-glide-slide v-for="slider in slideList" :key="slider.id">
              <div class="row text-center">
                <router-link :to="'/game-list/' + slider.game.id" class="text-black block">
                <div :style="{ backgroundImage: 'url(https://gamezone.digitalservices.website' + getSliderImage(slider) + ')',minHeight: '280px',backgroundSize: 'cover',backgroundPosition: 'center',backgroundRepeat: 'no-repeat'}"></div>
                <!-- <img class="shadow-2" :src="'https://gamezone.digitalservices.website' + slider.game.cover" :alt="slider.game.name_ar"> -->
                </router-link>
              </div>
        </vue-glide-slide>
      </vue-glide>
      <q-item style="max-width: 200px" v-if="skeleton">
      <q-item-section avatar>
        <q-skeleton type="QAvatar" />
      </q-item-section>

      <q-item-section>
        <q-item-label>
          <q-skeleton type="text" />
        </q-item-label>
        <q-item-label caption>
          <q-skeleton type="text" width="65%" />
        </q-item-label>
      </q-item-section>
    </q-item>
      </div>
    </div>
  </div>
</template>

<script>
var slidesUrl = 'https://gamezone.digitalservices.website/api/v1.0/slides'
import { Glide, GlideSlide } from 'vue-glide-js'
import 'vue-glide-js/dist/vue-glide.css'
export default {
  name: 'homeSlider',
  data () {
    return {
      skeleton: true,
      slideList: [],
      sliderConfig: {
        perView: 1,
        direction: 'rtl',
        autoplay: 2500,
        dragThreshold: 80,
        swipeThreshold: 120,
        rewind: true,
        bound: true,
        animationDuration: 900,
        breakpoints: {
          800: {
            perView: 1
          }
        }
      }
    }
  },
  mounted () {
    this.getSlides()
  },
  methods: {
    getSliderImage (slider) {
      return slider.game.cover || slider.game.thumbnail
    },
    getSlides () {
      this.$axios({
        method: 'get',
        url: slidesUrl,
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json'
          // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        }
      }).then(res => {
        this.slideList = res.data
        this.skeleton = false
      })
    }
  },
  components: {
    [Glide.name]: Glide,
    [GlideSlide.name]: GlideSlide
  }
}
</script>
