<template>
<section class="q-mt-sm">
    <vue-loadmore
    :loading-text="'جارِ التحميل...'"
    :finished-text="'لا يوجد المزيد'"
    :load-offset="200"
    :on-loadmore="onCategoriesLoad"
    :finished="categoriesFinished">

      <div v-for="category in categories" :key="category.id">
        <category-game-card :category="category" />
      </div>
    </vue-loadmore>
  </section>
</template>

<script>
import { http } from 'src/boot/axios'
var slidesUrl = 'https://gamezone.digitalservices.website/api/v1.0/slides'
var categoriesUrl = 'https://gamezone.digitalservices.website/api/v1.0/categories/paginated/categories'
import CategoryGameCard from './CategoryGameCard.vue'
export default {
  name: 'homeSlider',
  data () {
    return {
      slideList: [],
      categories: [],
      categoriesFinished: false,
      page: 1
    }
  },
  mounted () {
    this.getSlides()
    this.getCategories()
  },
  methods: {
    getSlides () {
      http.get(slidesUrl, {
        headers: {
          'Content-Type': 'application/json'
        //   // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        },
        cache: true
      }).then(res => {
        this.slideList = res.data
        this.skeleton = false
      })
    },
    onCategoriesLoad (done) {
      this.getCategories()
      // this.finished = true
      setTimeout(() => {
        done()
      }, 3000)
    },
    getCategories () {
      http.get(categoriesUrl, {
        headers: {
          'Content-Type': 'application/json'
        //   // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        },
        cache: true
      }).then(res => {
        this.categories = [...this.categories, ...res.data.data]
        this.skeleton = false
        this.page++
        if (this.page > res.data.last_page) {
          this.categoriesFinished = true
        }
      })
    }
  },
  components: {
    CategoryGameCard
  }
}
</script>
