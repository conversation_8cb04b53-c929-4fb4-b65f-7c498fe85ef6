<template>
  <div class="text-center q-px-sm md:q-px-xl" style="background: #ececec">
    <h6 class="text-white q-pt-xs q-mx-auto text-center">قائمة الألعاب</h6>
    <section class="text-center" v-if="this.$store.getters.user == null">
      <h6 class="q-my-md">
        <q-btn type="a" href="#" icon="verified" label="اشترك الآن وعش المغامرة!" color="orange" />
      </h6>
    </section>
    <div class="q-mx-auto q-mb-sm text-center">
        <q-btn type="a" href="#" color="lime-9" size="md" label="استمتع بالألعاب الجماعية من خلال التطبيق!" icon="download"/>
      </div>
    <div class="row">
      <div class="col-6 col-md-4" v-for="gameItem in gameList" :key="gameItem.id">
        <div class="q-pa-xs">
          <router-link :to="'/game-list/' + gameItem.id">
          <!-- <div style="max-height: 75px;overflow:hidden"> -->
            <img class="game_cover shadow-1" :src="'https://gamezone.digitalservices.website' + gameItem.cover" :alt="gameItem.name_ar">
          <!-- </div> -->
          </router-link>
        </div>
      </div>
    </div>
    <div class="q-mx-auto q-mb-sm text-center" v-if="isLoadMore">
        <q-btn @click="LoadMore" color="orange-6" size="md" label="تحميل المزيد.." icon="loop"/>
      </div>
  </div>
</template>
<style scoped>
.game_cover {
  max-width: 100%;
  border-radius: 6px;
}
</style>
<script>
var gamesUrl = 'https://gamezone.digitalservices.website/api/v1.0/games'
export default {
  name: 'GameList',
  data () {
    return {
      gameList: [],
      isLoadMore: false,
      nextPage: 0
    }
  },
  mounted () {
    this.getGameList()
  },
  methods: {
    LoadMore () {
      this.$axios({
        method: 'get',
        url: gamesUrl + '?page=' + this.nextPage,
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json'
          // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        }
      }).then(res => {
        if (res.data.meta.current_page < res.data.meta.last_page) {
          this.isLoadMore = true
          this.nextPage = res.data.meta.current_page + 1
        } else {
          this.isLoadMore = false
        }
        res.data.data.forEach(newGames => {
          this.gameList.push(newGames)
        })
      })
    },
    getGameList () {
      this.$axios({
        method: 'get',
        url: gamesUrl,
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json'
          // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        }
      }).then(res => {
        this.gameList = res.data.data
        if (res.data.meta.current_page < res.data.meta.last_page) {
          this.isLoadMore = true
          this.nextPage = res.data.meta.current_page + 1
        } else {
          this.isLoadMore = false
        }
        console.log(res.data)
      })
    }
  }
}
</script>
