import Vue from 'vue'
import axios from 'axios'
import { cacheAdapterEnhancer } from 'axios-extensions'

Vue.prototype.$axios = axios
// this will allow you to use this.$axios
// so you won't necessarily have to import axios in each vue file
// axios.defaults.headers.common['Access-Control-Allow-Origin'] = '*'
axios.defaults.baseURL = 'https://gamezone.digitalservices.website/api/v1.0/'

const http = axios.create({
  baseURL: 'https://mvas.digital:8000/api/v1.0/',
  headers: { 'Content-Type': 'application/json' },
  // disable the default cache and set the cache flag
  adapter: cacheAdapterEnhancer(axios.defaults.adapter, false)
})

export { axios, http }
