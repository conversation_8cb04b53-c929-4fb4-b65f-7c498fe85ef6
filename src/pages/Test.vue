<template>
  <div style="min-height:450px;position: relative">
    <section class="text-center" v-if="this.$store.getters.user == null && bookSingle.is_premium == true">
      <h6 class="q-mt-xl">
        <q-btn type="a" href="http://sudanipay.com/subscription/gaamek/gaamek/" icon="verified" label="الرجاء الإشتراك للمتابعة!" color="orange" />
      </h6>
    </section>
    <article else>
      <vue-pdf-app v-if="!preLoader" :config="config" @pages-rendered="alerto" :pdf="'https://mvas.digital:5000' + bookSingle.pdf" style="min-height:450px"></vue-pdf-app>
      <div class="pre_loader" v-show="preLoader">
          <h5 class="title" dir="rtl">
              <span><q-spinner-oval color="#e6b134" size="2em"/></span>
              <span>جاري التحميل، الرجاء الإنتظار..</span>
          </h5>
      </div>
    </article>
  </div>
</template>

<script>
import VuePdfApp from 'vue-pdf-app'
// import this to use default icons for buttons
import 'vue-pdf-app/dist/icons/main.css'
export default {
  data () {
    return {
      preLoader: true,
      bookSingle: null,
      config: {
        toolbar: {
          toolbarViewerRight: false
        }
      }
    }
  },
  mounted () {
    this.getBook()
  },
  components: {
    VuePdfApp
  },
  methods: {
    alerto () {
      // this.preLoader = false
      document.querySelector('html').setAttribute('dir', 'rtl')
      // alert('ff')
    },
    getBook () {
      this.$axios({
        method: 'get',
        url: 'books/' + this.$route.params.id + '/show',
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json'
          // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        }
      }).then(res => {
        this.bookSingle = res.data
        this.preLoader = false
        console.log(res.data)
      })
    }
  }
}
</script>
<style>
/* .book_show .q-drawer--left {
right: 0 !important;
left: auto !important;
direction: rtl !important;
}
.book_show .q-toolbar,
.book_show .the_footer
{
direction: rtl !important;
} */
.q-drawer {
  z-index: 9999999;
}
.pre_loader {
position: fixed;
top: 0;
background: #dfdfdf;
height: 100vh;
width: 100%;
border: 3px solid;
margin: 0 auto;
bottom: 0;
}
.pre_loader .title {
font-size: 20px;
position: absolute;
top: 46%;
width: 100%;
text-align: center;
color: #5e5e5e;
display: flex;
flex-direction: column;
}
#vuePdfApp, #outerContainer {
  min-height: 490px !important;
}

#sidebarToggle,
#sidebarContainer,
#secondaryDownload,
#secondaryPrint,
#secondaryToolbarToggle {
    display: none !important
}
</style>
