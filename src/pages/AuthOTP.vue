<template>
  <q-page class="auth-phone-pg" style="background: linear-gradient(rgb(118 16 52 / 45%), rgb(58 27 45 / 12%)), url('geometry.png')">
    <q-card class="my-card q-pa-md shadow border" flat>
    <section class="holder column justify-center items-center">
      <img src="icon.png" class="h-12" alt="">
      <img src="Logo.png" alt="" height="90">
      <p class="text-subtitle1">الرجاء إدخال الرمز الذي سيصلك على هاتفك</p>
      <div class="input_phone">
        <!-- OTP  -->
        <div id="opt_box">
          <div id="divInner">
          <input id="opt_input" lang="en-US" type="number" v-model="otpInput" maxlength="5" autofocus="autofocus"
          oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
          onKeyPress="if(this.value.length==5) return false;" />
          </div>
        </div>
        <!-- / OTP  -->
      </div>
      <p style="margin: 6px 0;color: red" v-show="optInvalid">أدخلت رمز غير صحيح، الرجاء المحاولة مرة أخرى</p>
      <p style="margin: 6px 0px;
    color: white;
    background: #761034;
    display: block;
    text-align: center;
    border-radius: 3px;
    padding: 6px 40px;" v-show="otpProcessing">جاري معالجة طلبك ..</p>
      <div v-show="otpProcessing" class="lds-ellipsis">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
      </div>
      <q-btn color="teal" class="submit" :disabled="!isDisableComputed" @click="verifyOtp" label="تأكيد" />
    </section>
    </q-card>
  </q-page>
</template>
<script>
var baseUrl = 'https://gamezone.digitalservices.website/api/v1.0/spay/'
export default {
  data () {
    return {
      otpInput: '',
      optInvalid: false,
      otpProcessing: false
    }
  },
  methods: {
    verifyOtp () {
      this.otpProcessing = true
      this.$axios({
        method: 'post',
        url: baseUrl + 'verify-otp',
        responseType: 'json',
        data: {
          msisdn: localStorage.getItem('gamek_the_phone'),
          otp: this.otpInput
        },
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => {
        console.log(res)
        // localStorage.setItem('gamek_the_access_token', res.data.access_token)
        // this.$router.push('/')
        // window.location.reload()
      }).catch((error) => {
        console.log(error)
        this.otpInput = ''
        this.optInvalid = true
        this.otpProcessing = false
      })
    }
  },
  computed: {
    isDisableComputed () {
      return this.otpInput.length >= 5
    }
  }
}
</script>
<style scoped>
.bg-teal {
  background: #761034 !important;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}
.holder {
  z-index: 6;
}
.auth-phone-pg {
  position: relative;
  direction: rtl;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.input_phone {
  padding: 6px 18px;
  border: 2px solid #ccc;
  border-radius: 9px;
  font-size: 20px;
  font-weight: bold;
}
.input_phone label {
  color: #5f5f5f;
  border-bottom: 2px solid #5f5f5f;
}
.input_phone .phonex {
  border: 0;
  outline: 0;
  padding: 5px 8px;
  letter-spacing: 6px;
  max-width: 170px;
}
.auth-phone-pg .submit {
  display: inline-block;
  margin: 10px 0;
  font-size: 18px;
  padding: 0px 10px;
}

.auth-phone-pg .logo {
  max-width: 100px;
  margin-bottom: 26px;
}
.auth-phone-pg .desc {
  margin: 50px auto;
  font-size: 16px;
  z-index: 5;
}
.footerx {
  position: absolute;
  bottom: 0;
  z-index: 1;
}
.quillx {
  max-width: 60px;
  margin-bottom: 0px;
  opacity: .9;
  z-index: 1;
}
/* OTP */
:focus {
outline: none;
}
#opt_input {
padding-right: 0px;
letter-spacing: 30px !important;
border: 0;
width: 232px;
text-align: center;
/* min-width: 220px; */
height: 30px;
}

#divInner {
right: 0;
position: sticky;
}

#opt_box {
width: 232px;
overflow: hidden;
margin: 0 auto;
}
</style>
