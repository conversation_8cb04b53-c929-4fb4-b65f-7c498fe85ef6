<template>
  <div>
    <TopSearchInput @submit="submit" />

    <div class="q-pa-sm">
      <h6 class="q-px-sm q-mt-none text-h6 cat_title text-weight-bold">
        <span v-if="isLoading">جارِ البحث...</span>
        <span v-else>البحث</span>
      </h6>
      <div class="row" v-if="!isLoading">
        <div class="col-6 rad-10 col-md-2 q-px-sm q-mb-sm" v-for="game in gameList" :key="game.id">
          <router-link :to="'/game-list/' + game.id" class="text-black">
            <div style="padding: 6px" class="shadow-1 rad-10">
              <img class="rad-10 max-w-100" width="100%" :src="'https://gamezone.digitalservices.website' + game.thumbnail" alt="">
              <h6 class="text-subtitle1 text-center q-ma-none text-weight-bold game-name">{{ game.name_ar }}</h6>
            </div>
          </router-link>
        </div>
      </div>
      <div class="text-center q-pa-lg q-my-lg" v-if="!isLoading && gameList.length == 0">
        لا تتوفر نتائج عن <strong>{{ q }}</strong>
      </div>
      <div class="row" v-if="isLoading">
        <div class="col-6 col-md-3 q-px-md" v-for="i in [1,2,3,4,5,6,7,8]" :key="i" >
          <div>
            <div>
              <q-skeleton type="QSlider" height="150px" />
            </div>

            <div>
                <q-skeleton type="text" height="50px" />
            </div>
          </div>
        </div>
      </div>
      <div class="q-mx-auto text-center" v-if="isLoadMore">
        <q-btn @click="getGames" color="orange-6" size="md" label="تحميل المزيد.." icon="loop"/>
      </div>
    </div>
  </div>
</template>

<script>
import TopSearchInput from 'src/components/TopSearchInput.vue'
import { http } from 'src/boot/axios'
var gamesUrl = 'https://gamezone.digitalservices.website/api/v1.0/games'
export default {
  components: { TopSearchInput },
  name: 'category',
  data () {
    return {
      gameList: [],
      isLoading: false,
      isLoadMore: false,
      listenBtn: '',
      playerShow: false,
      nextPage: 1,
      q: ''
    }
  },
  mounted () {
    this.q = this.$route.query.q
    this.getGames()
  },
  methods: {
    submit (query) {
      this.gameList = []
      this.q = query
      this.nextPage = 1
      this.getGames()
    },
    getGames () {
      this.isLoading = true
      http.get(gamesUrl + '?q=' + this.q + '&page=' + this.nextPage, {
        headers: {
          'Content-Type': 'application/json'
        //   // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        },
        cache: true
      }).then(res => {
        this.isLoading = false
        if (res.data.meta.current_page < res.data.meta.last_page) {
          this.isLoadMore = true
          this.nextPage = res.data.meta.current_page + 1
        } else {
          this.isLoadMore = false
        }
        res.data.data.forEach(newGames => {
          this.gameList.push(newGames)
        })
      })
    }
  }
}
</script>
