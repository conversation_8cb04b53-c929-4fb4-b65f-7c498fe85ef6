<template>
  <q-page>
    <TopSearchInput @submit="searchSubmit" />
    <Slider />
    <GameNewest />
    <GameCatList />
  </q-page>
</template>

<script>
import Slider from 'components/Home/Slider'
import GameCatList from 'src/components/Home/gameCatList.vue'
import GameNewest from 'src/components/Home/GameNewest.vue'
import TopSearchInput from 'src/components/TopSearchInput.vue'
export default {
  name: 'PageIndex',
  data () {
    return {
    }
  },
  components: {
    Slider, GameCatList, GameNewest, TopSearchInput
  },
  methods: {
    searchSubmit (query) {
      this.$router.push('/search?q=' + query)
    }
  }
}
</script>
