<template>
  <q-page class="terms_pg q-pa-md">
    <h5 class="text-blue-6 q-my-sm">Terms</h5>
    <div class="text-subtitle1" v-html="termsPage.content_ar">GamesClub ipsum dolor sit, amet consectetur adipisicing elit. Alias, tempore voluptatibus illo amet consectetur</div>
  </q-page>
</template>

<style scoped>
.terms_pg {
  background-position: bottom;
  padding-bottom: 10px;
}
a { text-decoration: none;}
body {
  font-size: 18px;
}
.q-avatar {
  font-size: 72px;
}
</style>
<script>
// var termsUrl = 'https://mvas.digital:7000/api/v1.0/dynamic-pages/b91f4d3e-6a6c-4307-8ed4-91955501d1de/show'
export default {
  name: 'terms',
  data () {
    return {
      termsPage: []
    }
  },
  mounted () {
    this.getPage()
  },
  methods: {
    getPage () {
      this.$axios({
        method: 'get',
        url: '',
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => {
        this.termsPage = res.data
      })
    }
  }
}
</script>
