<template>
  <q-page class="subscription_pg q-pa-none">
    <article class="subscription_pg_inner" v-if="pageContentLoaded">
      <!-- <div class="sub_img_holder rounded mt-2 q-mb-sm" style="height: 160px;overflow:hidden;background: url('subs.jpg')"></div> -->
      <div class="q-px-sm">
        <article class="knz-desc text-center q-py-md">
          <h5 class="q-ma-none q-mx-auto text-center" style="color:#761034">
            <img src="Logo.png" height="80px" alt="">
          </h5>
          <h6 class="text-red-9 text-red-9 q-my-sm" v-if="this.$route.params.msg != null">{{ this.$route.params.msg }}</h6>
        <p class="q-mt-sm q-mb-sm" style="color: #2c3e3e;font-size: 15px">ألعاب مميزة ، العب اونلاين و استمتع بي وقتك و نافس اصحابك.</p>
        <hr style="border: 1px solid #f9e2f0">
        <p class="q-mt-sm q-mb-md" style="color: #2c3e3e;font-size: 15px">قيمة الإشتراك اليومي <strong>16 جنيه فقط</strong> (شاملة الضريبة) تجدد يومياً عبر شبكة زين.</p>
        </article>
      </div>
    </article>
    <div class="auth-phone-pg" v-if="this.$store.getters.user == null">
      <section class="holderx column justify-center items-center">
        <a :href="landingPageURL">
          <q-btn color="primary" class="q-mt-md" icon="check_circle_outline" label="إشتراك" />
        </a>
      </section>
      </div>
      <article class="already_main q-py-sm" v-else>
        <p class="already_subs q-px-sm q-py-sm">أنت مشترك في الخدمة</p>
        <a @click="goHome" class="already_subs_btn q-mt-md q-px-sm q-py-sm">تصفح المحتوى</a>
    </article>
  </q-page>
</template>
<script>
var baseUrl = 'https://gamezone.digitalservices.website/api/v1.0/spay/'
var landingPageURL = 'http://sudanipay.com/subscription/gaamek/gaamek/'

export default {
  name: 'subscribe',
  data () {
    return {
      phonePrefix: '249',
      phoneInput: '',
      pageContent: {},
      landingPageURL: landingPageURL,
      pageContentLoaded: false,
      subClicked: false
    }
  },
  mounted () {
    this.pageContentLoaded = true
  },
  methods: {
    subWithPhone () {
      this.subClicked = true
      localStorage.setItem('gamek_the_phone', this.phonePrefix + this.phoneInput)
      this.$axios({
        method: 'post',
        url: baseUrl + 'subscribe',
        data: {
          msisdn: '249' + this.phoneInput
        },
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => {
        console.log(res.data)
        localStorage.setItem('gamek_the_phone', this.phonePrefix + this.phoneInput)
        // if (res.data.request_id) {
        //   localStorage.removeItem('request_id')
        //   localStorage.setItem('request_id', res.data.request_id)
        //   this.$router.push('/auth/otp')
        // } else {
        //   localStorage.setItem('gamek_the_access_token', res.data.access_token)
        //   this.$router.push({ name: 'home_router', params: { msg: 'لقد تم إشتراكك بنجاح في منصة أسرتي!' } })
        // }
      }).catch(res => {
        console.log('Error: ')
      })
    },
    goHome () {
      this.$router.push({ name: 'home_router' })
    }
  },
  computed: {
    isDisableComputed () {
      return this.phoneInput.length >= 9
    }
  }
}
</script>
<style scoped>
.knz-desc {
background: #fff5fb;
box-shadow: 0 0 3px #ccc;
border: 1px solid #ccc;
border-radius: 8px;
margin-top: 10px;
}
.already_subs {
  max-width: 300px;
  color: #761034;
  border-radius: 5px;
  margin: 0 auto 10px auto;
  text-align: center;
  background: #eff3f7;
}
.already_subs_btn {
  display: block;
  max-width: 300px;
  background: #761034;
  color: #fff;
  border-radius: 5px;
  text-align: center;
  margin: 0 auto;
  outline: 0;
  border: 0
}
.already_subs span, .already_subs_btn span {
font-size: 14px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}
.holderx {
  z-index: 6;
  padding-top: 5px;
}
.auth-phone-pg {
  position: relative;
  direction: rtl;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.input_phone {
  padding: 4px 14px;
  border: 2px solid #ccc;
  border-radius: 9px;
  font-size: 20px;
  font-weight: bold;
}
.input_phone label {
  color: #5f5f5f;
  border-bottom: 2px solid #5f5f5f;
}
.input_phone .phonex {
  border: 0;
  outline: 0;
  padding: 5px 8px;
  letter-spacing: 6px !important;
  max-width: 170px;
  border-radius: 5px;
}
.the_249 {
  margin-left: 4px;
}
.subscription_pg_inner {
  /* max-width: 500px; */
}
.sub_img_holder img {
  max-width: 100%;
}

</style>
