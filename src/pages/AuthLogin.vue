<template>
  <div class="q-pa-sm">
    <section class="the-box text-center q-my-lg q-py-md q-px-lg rad-10 shadow-2" style="min-height: 80vh;">
      <div>
        <form @submit.prevent="onSubmit" style="max-width: 400px; margin: 0 auto;">
          <q-card-section>
            <div class="text-center">
              <q-avatar size="xl" icon="login" color="green" text-color="white"  />
              <div class="text-h6 text-green q-my-md">تسجيل الدخول</div>
            </div>
          </q-card-section>

          <q-card-section class="q-pt-none text-dark">
            <!-- <p class="text-center">قم بإدخال رقم الهاتف إن كنت مشترك مسبقاً</p> -->
            <div class="q-mb-sm text-left">رقم الهاتف </div>
            <div>
              <q-input filled autofocus
                v-model="phone"
                type="phone"
                suffix="249"
                maxlength="9"
                :loading="loadingState"
                required
              >
                <!-- <template v-slot:before>
                  <q-icon name="phone" />
                </template> -->
              </q-input>

            </div>
            <!-- <q-input dense v-model="address" autofocus @keyup.enter="prompt = false" placeholder="0923456789" /> -->

            <div v-if="error_msg" class="text-red">{{ error_msg }}</div>
          </q-card-section>

          <q-btn type="submit"  class="text-white bg-theme-grad q-mb-lg" size="md" label="تسجيل الدخول" :disable="loadingState"  />
        </form>

      </div>
      <p class="text-center">مستخدم جديد؟ قم <a href="http://sudanipay.com/subscription/gaamek/gaamek/">باﻹشتراك من هنا</a></p>

    </section>
  </div>
</template>

<script>
export default {
  name: 'AuthLogin',
  data () {
    return {
      loadingState: false,
      phone: '',
      full_phone: '',
      error_msg: ''
    }
  },
  methods: {
    onSubmit () {
      this.error_msg = ''

      if (this.phone.length === 9) {
        this.full_phone = '249' + this.phone
        this.login()
      } else {
        this.error_msg = 'الرقم الذي ادخلته غير صحيح'
      }
    },

    login () {
      this.loadingState = true

      this.$axios.post('auth/login', {
        msisdn: this.full_phone,
        platform: 'gamek'
      }).then(res => {
        localStorage.setItem('gamek_the_access_token', res.data.access_token)
        localStorage.setItem('gamek_the_phone', this.full_phone)
        this.$store.dispatch('user', res.data.user)

        window.location.assign('/')
      }).catch((error) => {
        this.loadingState = false
        console.log(error.response.status)

        if (error.response && error.response.status === 401) {
          console.log(error.response.data)
          this.error_msg = error.response.data.message
        }
      })
    }
  }
}
</script>
