<template>
  <q-page class="flex flex-center column">
    <div class="text-center q-pa-md">
      <q-icon name="check_circle" color="positive" size="4rem" />
      <h2 class="text-h4 q-mt-md">شكراً لتسجيلك في منصة GAMEK</h2>
      <p class="text-subtitle1 q-mb-lg">سيتم تحويلك للصفحة الرئيسية خلال {{ countdown }} ثواني</p>
      <q-btn
        to="/"
        v-show="countdown <= 5"
        color="purple"
        label="الذهاب للصفحة الرئيسية"
        class="q-px-md"
        style="font-size: 14px"
      />
    </div>
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WFTWLXXC"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
  </q-page>
</template>

<script>
export default {
  name: 'ThankYou',
  data () {
    return {
      countdown: 10
    }
  },
  created () {
    this.initGTM()
  },
  mounted () {
    this.$nextTick(() => {
      this.startCountdown()
    })
  },
  beforeDestroy () {
    if (window.dataLayer) {
      delete window.dataLayer
    }
  },
  methods: {
    startCountdown () {
      const timer = setInterval(() => {
        this.countdown--
        if (this.countdown === 0) {
          clearInterval(timer)
          this.$router.push('/')
        }
      }, 1000)
    },
    initGTM () {
      window.dataLayer = window.dataLayer || []
      window.dataLayer.push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' })
      const script = document.createElement('script')
      script.async = true
      script.src = 'https://www.googletagmanager.com/gtm.js?id=GTM-WFTWLXXC'
      document.head.appendChild(script)
    }
  }
}
</script>

<style scoped>
.q-page {
  min-height: 60vh;
}
</style>
