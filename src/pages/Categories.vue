<template>
  <q-page padding>
    <div class="q-pa-sm" v-if="$router.currentRoute.name == 'category_list'">
      <h6 class="q-px-sm q-mt-none title-theme text-weight-bold">التصنيفات</h6>
      <div class="row games_categories">
        <div class="col-6 col-sm-4 rad-10 q-px-sm q-mb-sm games_category" v-for="category in catgList" :key="category.id">
          <router-link :to="'/categories/' + category.id" class="text-black">
            <div style="padding: 3px">
              <q-btn class="text-weight-bold" outline style="color: rgb(4, 31, 64));width:100%" :label="category.name_ar" />
            </div>
          </router-link>
        </div>
      </div>
    </div>
    <router-view />
  </q-page>
</template>

<script>
import { http } from 'src/boot/axios'
var catgListUrl = 'https://gamezone.digitalservices.website/api/v1.0/categories'
export default {
  name: 'categories',
  data () {
    return {
      catgList: []
    }
  },
  mounted () {
    this.getCatgList()
  },
  methods: {
    getCatgList () {
      http.get(catgListUrl, {
        headers: {
          'Content-Type': 'application/json'
        //   // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        },
        cache: true
      }).then(res => {
        this.catgList = res.data
      })
    }
  }
}
</script>
