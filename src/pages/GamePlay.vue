<template>
    <div>
      <q-btn class="close_btn" round color="red-7" icon="clear" to="/" />
      <iframe v-if="this.$store.getters.user != null && this.$store.getters.user.has_active_billing == true" class="game_frame" :src="game.url" frameborder="0"></iframe>
      <section class="full_subs text-center" v-else-if="this.$store.getters.user != null && this.$store.getters.user.has_active_billing == false">
        <h6 class="q-mt-xl">
          <q-btn icon="verified" label="الرجاء التحقق من الرصيد والمحاولة مرة أخرى!" color="orange" />
        </h6>
      </section>
      <section class="full_subs text-center" v-else-if="this.$store.getters.user == null">
        <h6>
          <q-btn type="a" href="#" icon="verified" label="اشترك الآن وعش المغامرة!" color="lime-8" />
        </h6>
      </section>
        <!-- <embed style="height:100vh;width:100%;border:none;" :src="game.url" /> -->
    </div>
</template>

<style>
.full_subs {
  background: indigo;
  background: linear-gradient(rgb(39 32 51 / 24%),rgb(14 13 13 / 42%)),url('../../public/img/blur.jpg');
  background-size: cover;
  background-position: center;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.close_btn {
    position: fixed;
    bottom: 12px;
    right: 14px;
    font-size: 14px;
    z-index: 9999;
}
.game_single .q-header {
  display: none
 }
.game_frame {
    height: 100vh !important;
    width: 100% !important;
}
</style>

<script>
var gameUrl = 'https://gamezone.digitalservices.website/api/v1.0/games/'
// library/favorite
export default {
  data () {
    return {
      game: {}
    }
  },
  mounted () {
    this.getGame()
  },

  methods: {
    getGame () {
      this.$axios({
        method: 'get',
        url: gameUrl + this.$route.params.id + '/show',
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json'
          // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        }
      }).then(res => {
        this.game = res.data
      })
    }
  }
}
</script>
