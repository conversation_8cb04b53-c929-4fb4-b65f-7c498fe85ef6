<template>
<section>
    <div class="q-pa-sm" v-if="!frameVisibil">
      <section class="the-box text-center q-py-sm q-px-lg rad-10 shadow-2" style="margin-top: 60px">
        <img class="rad-10" :src="'https://gamezone.digitalservices.website' + game.thumbnail" alt="" style="max-width: 100px">
        <h6 class="game-name text-center text-weight-bold">{{ game.name_ar }}</h6>
        <p class="text-center" v-if="game.description_ar">{{ game.description_ar }}</p>
        <p class="text-center">تاريخ التحميل: {{ new Date().toLocaleDateString() }}</p>
        <div class="text-center q-py-lg" v-if="game.screenshots && game.screenshots.length > 0">
          <q-btn @click="play" class="text-white bg-theme-grad" size="md" label="العب الآن" />
        </div>
        <article v-if="game.screenshots && game.screenshots.length > 0">
          <h6 class="text-center title-theme text-weight-bold q-mb-0">صور من اللعبة</h6>
          <div class="row">
            <div v-for="shot in game.screenshots" :key="shot.url" class="col-6 q-pa-sm">
              <img class="max" :src="'https://gamezone.digitalservices.website' + shot.url" alt="">
            </div>
          </div>
        </article>

        <div v-if="this.$store.getters.user != null && this.$store.getters.user.has_active_billing == true">
          <q-btn @click="play" class="text-white bg-theme-grad" size="md" label="العب الآن" />
        </div>
        <div class="full_subs text-center" v-else-if="this.$store.getters.user != null && this.$store.getters.user.has_active_billing == false">
          <h6 class="q-mt-xl">
            <q-btn icon="verified" label="الرجاء التحقق من الرصيد والمحاولة مرة أخرى!" color="orange" />
          </h6>
        </div>
        <div class="text-center" v-if="$store.getters.user == null">
          <h6 class="q-mt-xl">
            <a :href="landingPageURL">
              <q-btn icon="verified" label="الرجاء الإشتراك لبدء اللعبة!" color="green" />
            </a>
          </h6>
        </div>
      </section>
    </div>
    <!-- ------------- -->
    <div v-if="frameVisibil">
        <q-btn class="close_btn" round color="red-7" icon="clear" @click="close" />
        <iframe class="game_frame" :src="game.url" frameborder="0"></iframe>
    </div>
</section>
</template>

<script>
import { http } from 'src/boot/axios'
var gameUrl = 'https://gamezone.digitalservices.website/api/v1.0/games/'
var landingPageURL = 'http://sudanipay.com/subscription/gaamek/gaamek/'

// library/favorite
export default {
  data () {
    return {
      game: {},
      landingPageURL: landingPageURL,
      frameVisibil: false
    }
  },
  mounted () {
    this.getGame()
  },

  methods: {
    getGame () {
      http.get(gameUrl + this.$route.params.id + '/show', {
        headers: {
          'Content-Type': 'application/json'
        //   // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        },
        cache: true
      }).then(res => {
        this.game = res.data
      })
    },
    play () {
      this.toggleFullScreen()
      this.frameVisibil = true
    },
    close () {
      this.toggleFullScreen()
      this.frameVisibil = false
    },
    toggleFullScreen () {
      // Get your full screen element
      try {
        const body = document.body
        const rfs = body.requestFullscreen || body.webkitRequestFullScreen || body.mozRequestFullScreen || body.msRequestFullscreen
        rfs.call(body)
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>
<style>
.full_subs {
  background: indigo;
  background: linear-gradient(rgb(39 32 51 / 24%),rgb(14 13 13 / 42%)),url('../../public/img/blur.jpg');
  background-size: cover;
  background-position: center;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.close_btn {
    position: fixed;
    bottom: 12px;
    right: 14px;
    font-size: 14px;
    z-index: 99999;
}
.game_single .q-header {
  /* display: none */
 }
.game_frame {
    height: 100vh!important;
    width: 100%!important;
    position: absolute;
    z-index: 9999;
}
</style>
