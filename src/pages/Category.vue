<template>
    <div class="q-pa-sm" v-if="$router.currentRoute.name == 'category_single'">
      <h6 class="q-px-sm q-mt-none text-h6 cat_title text-weight-bold">{{ catgSingle.name_ar }}</h6>
      <div class="row">
        <div class="col-6 rad-10 col-md-2 q-px-sm q-mb-sm" v-for="game in gameList" :key="game.id">
          <router-link :to="'/game-list/' + game.id" class="text-black">
            <div style="padding: 6px" class="shadow-1 rad-10">
              <img class="rad-10 max-w-100" width="100%" :src="'https://gamezone.digitalservices.website' + game.thumbnail" alt="">
              <h6 class="text-subtitle1 text-center q-ma-none text-weight-bold game-name">{{ game.name_ar }}</h6>
            </div>
          </router-link>
        </div>
      </div>
      <div class="q-mx-auto text-center" v-if="isLoadMore">
        <q-btn @click="getCatgGames" color="orange-6" size="md" label="تحميل المزيد.." icon="loop"/>
      </div>
    </div>
</template>

<script>
import { http } from 'src/boot/axios'
var categoryUrl = 'https://gamezone.digitalservices.website/api/v1.0/categories/'
export default {
  name: 'category',
  data () {
    return {
      catgSingle: {},
      gameList: [],
      isLoadMore: false,
      listenBtn: '',
      playerShow: false,
      nextPage: 1
    }
  },
  mounted () {
    this.getCatg()
    this.getCatgGames()
  },
  methods: {
    getCatgGames () {
      http.get(categoryUrl + this.$route.params.id + '/games?page=' + this.nextPage, {
        headers: {
          'Content-Type': 'application/json'
        //   // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        },
        cache: true
      }).then(res => {
        if (res.data.meta.current_page < res.data.meta.last_page) {
          this.isLoadMore = true
          this.nextPage = res.data.meta.current_page + 1
        } else {
          this.isLoadMore = false
        }
        res.data.data.forEach(newGames => {
          this.gameList.push(newGames)
        })
      })
    },
    getCatg () {
      this.$axios({
        method: 'get',
        url: categoryUrl + this.$route.params.id,
        responseType: 'json',
        headers: {
          'Content-Type': 'application/json'
          // Authorization: 'Bearer ' + localStorage.getItem('gamek_the_access_token')
        }
      }).then(res => {
        this.catgSingle = res.data
      })
    }
  }
}
</script>
